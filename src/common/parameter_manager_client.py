from typing import Optional

from google.cloud import parametermanager_v1

from common.consts import GCP_REGION
from common.logger import get_logger

logger = get_logger()


def get_parameter(
    parameter_id: str,
    project_id: str,
    version: str = "latest",
    region: str = GCP_REGION,
) -> Optional[str]:
    """
    Retrieves a parameter from the Google Parameter Manager.
    Parameter id can be passed as:
     - relative name: parameter_id
     - fully qualified name: projects/{{project}}/locations/europe-west3/parameters/{{parameter_id}}

    Reference: https://cloud.google.com/secret-manager/parameter-manager/docs/view-parameter-version-details

    :param parameter_id: parameter id
    :param project_id: google project id
    :param region: google region
    :param version: optional parameter version number or alias, defaults to `latest`

    :returns: parameter payload if found, None otherwise
    """

    client = parametermanager_v1.ParameterManagerClient()

    project_path = f"projects/{project_id}/locations/{region}/parameters"
    if project_path in parameter_id:
        name = parameter_id
    else:
        name = f"{project_path}/{parameter_id}"

    if version != "latest":
        name = f"{name}/versions/{version}"
    else:
        # Find the latest version by listing all versions and sorting by most recent update_time
        request = parametermanager_v1.ListParameterVersionsRequest(parent=name)  # noqa
        versions = list(client.list_parameter_versions(request=request))
        latest_version = max(versions, key=lambda v: v.update_time)

        if not latest_version:
            logger.warning(f"No latest versions found for '{name}' parameter!")
            return None

        name = latest_version.name

    request = parametermanager_v1.GetParameterVersionRequest(name=name)  # noqa
    response = client.get_parameter_version(request=request)
    payload = response.payload.data.decode("utf-8")

    if not payload:
        logger.warning(f"No payload found for '{name}' parameter!")
        return None

    return payload


if __name__ == "__main__":
    from common.consts import DEFAULT_GOOGLE_PROJECT_ID

    parameter_value = get_parameter(parameter_id="test", project_id=DEFAULT_GOOGLE_PROJECT_ID)

    print(f"Value: {parameter_value}")
